"use client";

import { cn } from "@/lib/utils";
import { createClient } from "@/lib/supabase/client";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

export function LoginForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"div">) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isOAuthLoading, setIsOAuthLoading] = useState(false);
  const router = useRouter();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    const supabase = createClient();
    setIsLoading(true);
    setError(null);

    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      if (error) throw error;
      // Redirect to editor after successful login
      router.push("/editor");
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const handleOAuthLogin = async (provider: 'twitter') => {
    const supabase = createClient();
    setIsOAuthLoading(true);
    setError(null);

    try {
      // Detect platform
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      const isAndroid = /Android/.test(navigator.userAgent);
      const isMobile = isIOS || isAndroid;
      
      // Get OAuth URL from Supabase
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          skipBrowserRedirect: isMobile, // Skip auto-redirect on mobile to handle it ourselves
          queryParams: {
            display: isMobile ? 'touch' : 'page',
          },
        },
      });
      
      if (error) throw error;
      
      if (data?.url && isMobile) {
        // Try to open Twitter app on mobile
        const attemptAppOpen = () => {
          let appOpened = false;
          // eslint-disable-next-line prefer-const
          let timeoutId: NodeJS.Timeout | undefined;
          
          // Create app-specific URLs
          let appUrl: string;
          
          if (isAndroid) {
            // Android Intent URL
            const oauthParams = data.url.split('?')[1];
            appUrl = `intent://oauth/authorize?${oauthParams}#Intent;package=com.twitter.android;scheme=https;end`;
          } else if (isIOS) {
            // iOS URL scheme
            appUrl = data.url.replace('https://api.twitter.com', 'twitter://api.twitter.com');
          } else {
            // Fallback to web
            window.location.href = data.url;
            return;
          }
          
          // Set up page visibility change detection
          const handleVisibilityChange = () => {
            if (document.hidden) {
              appOpened = true;
              if (timeoutId) clearTimeout(timeoutId);
              document.removeEventListener('visibilitychange', handleVisibilityChange);
            }
          };
          
          document.addEventListener('visibilitychange', handleVisibilityChange);
          
          // Attempt to open the app
          const startTime = Date.now();
          window.location.href = appUrl;
          
          // Fallback to web OAuth if app doesn't open
          timeoutId = setTimeout(() => {
            document.removeEventListener('visibilitychange', handleVisibilityChange);
            
            // If page is still visible and not much time passed, app likely didn't open
            if (!document.hidden && !appOpened && (Date.now() - startTime < 3000)) {
              // Fallback to web OAuth
              window.location.href = data.url;
            }
          }, 2500);
        };
        
        attemptAppOpen();
      } else if (data?.url) {
        // Desktop or fallback - direct redirect
        window.location.href = data.url;
      }
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : "An error occurred");
      setIsOAuthLoading(false);
    }
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">Login</CardTitle>
          <CardDescription>
            Enter your email below to login to your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin}>
            <div className="flex flex-col gap-6">
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <div className="flex items-center">
                  <Label htmlFor="password">Password</Label>
                  <Link
                    href="/auth/forgot-password"
                    className="ml-auto inline-block text-sm underline-offset-4 hover:underline"
                  >
                    Forgot your password?
                  </Link>
                </div>
                <Input
                  id="password"
                  type="password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
              {error && <p className="text-sm text-red-500">{error}</p>}
              <Button type="submit" className="w-full" disabled={isLoading || isOAuthLoading}>
                {isLoading ? "Logging in..." : "Login"}
              </Button>
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-background px-2 text-muted-foreground">
                    Or continue with
                  </span>
                </div>
              </div>
              <Button
                type="button"
                variant="outline"
                className="w-full"
                onClick={() => handleOAuthLogin('twitter')}
                disabled={isOAuthLoading || isLoading}
              >
                <svg
                  className="mr-2 h-4 w-4"
                  aria-hidden="true"
                  focusable="false"
                  data-prefix="fab"
                  data-icon="x-twitter"
                  role="img"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 512 512"
                >
                  <path
                    fill="currentColor"
                    d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z"
                  ></path>
                </svg>
                {isOAuthLoading ? "Connecting..." : "Continue with X"}
              </Button>
            </div>
            <div className="mt-4 text-center text-sm">
              Don&apos;t have an account?{" "}
              <Link
                href="/auth/sign-up"
                className="underline underline-offset-4"
              >
                Sign up
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
